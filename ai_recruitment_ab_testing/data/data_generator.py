"""
AI招聘A/B测试数据生成模块
生成符合实验设计要求的真实数据
"""

import random
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import uuid

from ..core.experimental_factors import (
    ExperimentalFactorDesign, 
    RecruitmentStage, 
    ParticipantRole,
    ExperimentalFactor
)

@dataclass
class Participant:
    """参与者数据类"""
    participant_id: str
    name: str
    role: ParticipantRole
    group: str  # "实验组" 或 "对照组"
    experience_years: int
    department: str
    invited: bool
    participated: bool
    recruitment_stages_involved: List[RecruitmentStage]

@dataclass
class ExperimentalData:
    """实验数据类"""
    data_id: str
    participant_id: str
    factor_id: str
    stage: RecruitmentStage
    measurement_value: float
    measurement_date: datetime
    notes: Optional[str] = None

class RecruitmentDataGenerator:
    """招聘数据生成器"""
    
    def __init__(self, seed: int = 42):
        """初始化数据生成器"""
        random.seed(seed)
        np.random.seed(seed)
        self.factor_design = ExperimentalFactorDesign()
        self.participants = []
        self.experimental_data = []
        
        # 中文姓名库
        self.surnames = ["张", "王", "李", "赵", "陈", "刘", "杨", "黄", "周", "吴", 
                        "徐", "孙", "马", "朱", "胡", "林", "郭", "何", "高", "罗"]
        self.given_names = ["伟", "芳", "娜", "敏", "静", "丽", "强", "磊", "军", "洋",
                           "勇", "艳", "杰", "涛", "明", "超", "秀英", "华", "玲", "建华"]
        
        # 部门列表
        self.departments = ["人力资源部", "技术部", "市场部", "销售部", "财务部", 
                           "运营部", "产品部", "客服部"]
    
    def generate_participants(self) -> List[Participant]:
        """生成参与者数据"""
        participants = []
        
        # 生成96名邀请参与者
        for i in range(96):
            participant_id = f"P{i+1:03d}"
            name = random.choice(self.surnames) + random.choice(self.given_names)
            role = random.choice(list(ParticipantRole))
            department = random.choice(self.departments)
            experience_years = random.randint(1, 15)
            
            # 确定是否实际参与（84人参与，12人未参与）
            participated = i < 84
            
            # 分组：实验组39人，对照组45人
            if participated:
                if i < 39:
                    group = "实验组"
                else:
                    group = "对照组"
            else:
                # 未参与者随机分组
                group = random.choice(["实验组", "对照组"])
            
            # 根据角色确定参与的招聘阶段
            stages_involved = self._determine_stages_by_role(role)
            
            participant = Participant(
                participant_id=participant_id,
                name=name,
                role=role,
                group=group,
                experience_years=experience_years,
                department=department,
                invited=True,
                participated=participated,
                recruitment_stages_involved=stages_involved
            )
            
            participants.append(participant)
        
        self.participants = participants
        return participants
    
    def _determine_stages_by_role(self, role: ParticipantRole) -> List[RecruitmentStage]:
        """根据角色确定参与的招聘阶段"""
        if role == ParticipantRole.RECRUITMENT_TEAM:
            return [RecruitmentStage.RESUME_SCREENING, RecruitmentStage.INITIAL_EVALUATION, 
                   RecruitmentStage.FINAL_DECISION]
        elif role == ParticipantRole.INTERVIEWER:
            return [RecruitmentStage.INTERVIEW, RecruitmentStage.FINAL_DECISION]
        else:  # SYSTEM_RECRUITER
            return list(RecruitmentStage)
    
    def generate_experimental_data(self) -> List[ExperimentalData]:
        """生成实验数据"""
        experimental_data = []
        factors = self.factor_design.get_all_factors()
        
        # 只为实际参与的84人生成数据
        participating_participants = [p for p in self.participants if p.participated]
        
        for participant in participating_participants:
            for stage in participant.recruitment_stages_involved:
                # 获取该阶段适用的因子
                stage_factors = self.factor_design.get_factors_by_stage(stage)
                role_factors = self.factor_design.get_factors_by_role(participant.role)
                
                # 取交集
                applicable_factors = [f for f in stage_factors if f in role_factors]
                
                for factor in applicable_factors:
                    # 生成测量值
                    measurement_value = self._generate_measurement_value(
                        factor, participant.group, participant.experience_years
                    )
                    
                    # 生成测量日期（实验期间的随机日期）
                    base_date = datetime(2024, 3, 1)
                    random_days = random.randint(0, 60)
                    measurement_date = base_date + timedelta(days=random_days)
                    
                    data = ExperimentalData(
                        data_id=str(uuid.uuid4()),
                        participant_id=participant.participant_id,
                        factor_id=factor.factor_id,
                        stage=stage,
                        measurement_value=measurement_value,
                        measurement_date=measurement_date,
                        notes=f"{participant.role.value}在{stage.value}阶段的{factor.factor_name}测量"
                    )
                    
                    experimental_data.append(data)
        
        self.experimental_data = experimental_data
        return experimental_data
    
    def _generate_measurement_value(self, factor: ExperimentalFactor, 
                                   group: str, experience_years: int) -> float:
        """生成测量值"""
        # 基线值
        baseline_min, baseline_max = factor.baseline_range
        baseline_value = random.uniform(baseline_min, baseline_max)
        
        # 经验调整（经验越多，基线值略高）
        experience_adjustment = (experience_years - 5) * 0.1
        baseline_value += experience_adjustment
        
        # AI影响
        if group == "实验组":
            ai_impact_min, ai_impact_max = factor.ai_impact_range
            ai_impact = random.uniform(ai_impact_min, ai_impact_max)
            
            # 添加一些随机性，使得实验组不总是优于对照组
            if factor.factor_id in ["processing_time", "workload_stress"]:
                # 对于这些因子，值越低越好
                # 有30%的概率实验组表现更差
                if random.random() < 0.3:
                    ai_impact = abs(ai_impact) * 0.5
            elif factor.factor_id in ["decision_accuracy", "candidate_satisfaction"]:
                # 对于这些因子，值越高越好
                # 有25%的概率实验组表现更差
                if random.random() < 0.25:
                    ai_impact = -abs(ai_impact) * 0.3
            
            final_value = baseline_value + ai_impact
        else:
            # 对照组：基线值加上小幅随机波动
            random_variation = random.uniform(-0.5, 0.5)
            final_value = baseline_value + random_variation
        
        # 确保值在合理范围内
        if "时间" in factor.factor_name:
            final_value = max(10.0, final_value)  # 最少10分钟
        elif "评分" in factor.factor_name or "满意度" in factor.factor_name:
            final_value = max(1.0, min(10.0, final_value))  # 1-10分范围
        
        return round(final_value, 2)
    
    def export_to_dataframes(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """导出为DataFrame格式"""
        # 参与者数据
        participants_df = pd.DataFrame([asdict(p) for p in self.participants])
        
        # 实验数据
        experimental_data_dict = []
        for data in self.experimental_data:
            data_dict = asdict(data)
            data_dict['stage'] = data.stage.value
            experimental_data_dict.append(data_dict)
        
        experimental_df = pd.DataFrame(experimental_data_dict)
        
        return participants_df, experimental_df
    
    def generate_summary_statistics(self) -> Dict:
        """生成汇总统计信息"""
        participating_participants = [p for p in self.participants if p.participated]
        
        # 分组统计
        experimental_group = [p for p in participating_participants if p.group == "实验组"]
        control_group = [p for p in participating_participants if p.group == "对照组"]
        
        # 角色分布
        role_distribution = {}
        for role in ParticipantRole:
            role_count = len([p for p in participating_participants if p.role == role])
            role_distribution[role.value] = role_count
        
        # 经验分布
        experience_stats = {
            "平均经验年限": np.mean([p.experience_years for p in participating_participants]),
            "经验年限标准差": np.std([p.experience_years for p in participating_participants]),
            "最小经验年限": min([p.experience_years for p in participating_participants]),
            "最大经验年限": max([p.experience_years for p in participating_participants])
        }
        
        summary = {
            "总邀请人数": len(self.participants),
            "实际参与人数": len(participating_participants),
            "实验组人数": len(experimental_group),
            "对照组人数": len(control_group),
            "角色分布": role_distribution,
            "经验统计": experience_stats,
            "数据点总数": len(self.experimental_data)
        }
        
        return summary

if __name__ == "__main__":
    # 测试数据生成
    generator = RecruitmentDataGenerator(seed=42)
    
    print("生成参与者数据...")
    participants = generator.generate_participants()
    
    print("生成实验数据...")
    experimental_data = generator.generate_experimental_data()
    
    print("生成汇总统计...")
    summary = generator.generate_summary_statistics()
    
    print("\n实验数据汇总:")
    print("=" * 50)
    for key, value in summary.items():
        print(f"{key}: {value}")
