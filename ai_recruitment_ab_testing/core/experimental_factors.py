"""
AI招聘A/B测试实验因子设计模块
设计涵盖招聘全流程的实验因子，确保对实验组和对照组都适用
"""

from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional
from enum import Enum
import random
import numpy as np

class RecruitmentStage(Enum):
    """招聘阶段枚举"""
    RESUME_SCREENING = "简历筛选"
    INITIAL_EVALUATION = "初步评估"
    INTERVIEW = "面试环节"
    FINAL_DECISION = "最终决策"

class ParticipantRole(Enum):
    """参与者角色枚举"""
    RECRUITMENT_TEAM = "招聘团队"
    INTERVIEWER = "面试官"
    SYSTEM_RECRUITER = "系统招聘专员"

@dataclass
class ExperimentalFactor:
    """实验因子数据类"""
    factor_id: str
    factor_name: str
    description: str
    measurement_scale: str  # 测量量表类型
    applicable_stages: List[RecruitmentStage]
    applicable_roles: List[ParticipantRole]
    baseline_range: Tuple[float, float]  # 基线值范围
    ai_impact_range: Tuple[float, float]  # AI影响范围（可正可负）

class ExperimentalFactorDesign:
    """实验因子设计类"""
    
    def __init__(self):
        self.factors = self._initialize_factors()
    
    def _initialize_factors(self) -> Dict[str, ExperimentalFactor]:
        """初始化实验因子"""
        factors = {}
        
        # 效率相关因子
        factors["processing_time"] = ExperimentalFactor(
            factor_id="processing_time",
            factor_name="处理时间效率",
            description="完成招聘任务所需的平均时间（分钟）",
            measurement_scale="连续变量",
            applicable_stages=[stage for stage in RecruitmentStage],
            applicable_roles=[role for role in ParticipantRole],
            baseline_range=(45.0, 120.0),
            ai_impact_range=(-30.0, 15.0)  # AI可能减少时间，但也可能增加学习成本
        )
        
        factors["decision_accuracy"] = ExperimentalFactor(
            factor_id="decision_accuracy",
            factor_name="决策准确性",
            description="招聘决策的准确性评分（1-10分）",
            measurement_scale="李克特量表",
            applicable_stages=[RecruitmentStage.INITIAL_EVALUATION, 
                             RecruitmentStage.INTERVIEW, 
                             RecruitmentStage.FINAL_DECISION],
            applicable_roles=[role for role in ParticipantRole],
            baseline_range=(6.0, 8.5),
            ai_impact_range=(-1.0, 2.0)  # AI可能提高准确性，但也可能产生偏见
        )
        
        factors["candidate_satisfaction"] = ExperimentalFactor(
            factor_id="candidate_satisfaction",
            factor_name="候选人满意度",
            description="候选人对招聘流程的满意度评分（1-10分）",
            measurement_scale="李克特量表",
            applicable_stages=[stage for stage in RecruitmentStage],
            applicable_roles=[ParticipantRole.RECRUITMENT_TEAM, ParticipantRole.SYSTEM_RECRUITER],
            baseline_range=(6.5, 8.0),
            ai_impact_range=(-1.5, 1.8)  # AI可能提高体验，但也可能显得冷漠
        )
        
        factors["bias_detection"] = ExperimentalFactor(
            factor_id="bias_detection",
            factor_name="偏见识别能力",
            description="识别和减少招聘偏见的能力评分（1-10分）",
            measurement_scale="李克特量表",
            applicable_stages=[RecruitmentStage.RESUME_SCREENING, 
                             RecruitmentStage.INITIAL_EVALUATION],
            applicable_roles=[role for role in ParticipantRole],
            baseline_range=(4.0, 7.0),
            ai_impact_range=(-0.5, 3.0)  # AI在减少偏见方面通常有优势
        )
        
        factors["workload_stress"] = ExperimentalFactor(
            factor_id="workload_stress",
            factor_name="工作负荷压力",
            description="参与者感受到的工作压力水平（1-10分）",
            measurement_scale="李克特量表",
            applicable_stages=[stage for stage in RecruitmentStage],
            applicable_roles=[role for role in ParticipantRole],
            baseline_range=(5.0, 8.5),
            ai_impact_range=(-2.0, 1.5)  # AI通常减少压力，但可能增加技术焦虑
        )
        
        factors["skill_development"] = ExperimentalFactor(
            factor_id="skill_development",
            factor_name="技能发展感知",
            description="参与者认为自己技能得到发展的程度（1-10分）",
            measurement_scale="李克特量表",
            applicable_stages=[stage for stage in RecruitmentStage],
            applicable_roles=[role for role in ParticipantRole],
            baseline_range=(5.5, 7.5),
            ai_impact_range=(-1.0, 2.5)  # AI可能促进学习，但也可能产生依赖
        )
        
        factors["cost_effectiveness"] = ExperimentalFactor(
            factor_id="cost_effectiveness",
            factor_name="成本效益",
            description="招聘流程的成本效益评估（1-10分）",
            measurement_scale="李克特量表",
            applicable_stages=[stage for stage in RecruitmentStage],
            applicable_roles=[ParticipantRole.RECRUITMENT_TEAM, ParticipantRole.SYSTEM_RECRUITER],
            baseline_range=(5.0, 7.5),
            ai_impact_range=(-1.0, 2.8)  # AI长期看成本效益好，但初期投入大
        )
        
        factors["human_ai_collaboration"] = ExperimentalFactor(
            factor_id="human_ai_collaboration",
            factor_name="人机协作效果",
            description="人工智能与人类协作的效果评分（1-10分）",
            measurement_scale="李克特量表",
            applicable_stages=[stage for stage in RecruitmentStage],
            applicable_roles=[role for role in ParticipantRole],
            baseline_range=(4.0, 6.0),  # 对照组基线较低
            ai_impact_range=(0.5, 4.0)  # 实验组在此因子上有优势
        )
        
        return factors
    
    def get_factor(self, factor_id: str) -> Optional[ExperimentalFactor]:
        """获取指定因子"""
        return self.factors.get(factor_id)
    
    def get_factors_by_stage(self, stage: RecruitmentStage) -> List[ExperimentalFactor]:
        """根据招聘阶段获取适用因子"""
        return [factor for factor in self.factors.values() 
                if stage in factor.applicable_stages]
    
    def get_factors_by_role(self, role: ParticipantRole) -> List[ExperimentalFactor]:
        """根据参与者角色获取适用因子"""
        return [factor for factor in self.factors.values() 
                if role in factor.applicable_roles]
    
    def get_all_factors(self) -> Dict[str, ExperimentalFactor]:
        """获取所有因子"""
        return self.factors.copy()

class FactorBalanceValidator:
    """因子平衡性验证器"""
    
    @staticmethod
    def validate_factor_balance(factors: Dict[str, ExperimentalFactor]) -> Dict[str, bool]:
        """验证因子设计的平衡性"""
        validation_results = {}
        
        for factor_id, factor in factors.items():
            # 检查AI影响范围是否包含负值（避免单向偏差）
            has_negative_impact = factor.ai_impact_range[0] < 0
            has_positive_impact = factor.ai_impact_range[1] > 0
            
            # 平衡性要求：AI影响应该既有正面也有负面的可能性
            is_balanced = has_negative_impact or (
                has_positive_impact and abs(factor.ai_impact_range[0]) > 0.1
            )
            
            validation_results[factor_id] = is_balanced
        
        return validation_results
    
    @staticmethod
    def generate_balance_report(factors: Dict[str, ExperimentalFactor]) -> str:
        """生成平衡性报告"""
        validation_results = FactorBalanceValidator.validate_factor_balance(factors)
        
        report = "实验因子平衡性验证报告\n"
        report += "=" * 50 + "\n\n"
        
        balanced_count = sum(validation_results.values())
        total_count = len(validation_results)
        
        report += f"总因子数量: {total_count}\n"
        report += f"平衡因子数量: {balanced_count}\n"
        report += f"平衡性比例: {balanced_count/total_count:.2%}\n\n"
        
        report += "各因子平衡性详情:\n"
        for factor_id, is_balanced in validation_results.items():
            factor = factors[factor_id]
            status = "✓ 平衡" if is_balanced else "✗ 不平衡"
            report += f"- {factor.factor_name} ({factor_id}): {status}\n"
            report += f"  AI影响范围: {factor.ai_impact_range}\n\n"
        
        return report

if __name__ == "__main__":
    # 测试因子设计
    design = ExperimentalFactorDesign()
    validator = FactorBalanceValidator()
    
    print("AI招聘A/B测试实验因子设计")
    print("=" * 50)
    
    factors = design.get_all_factors()
    print(f"设计因子总数: {len(factors)}")
    
    # 生成平衡性报告
    balance_report = validator.generate_balance_report(factors)
    print("\n" + balance_report)
